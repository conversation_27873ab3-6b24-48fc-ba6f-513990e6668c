import { streamText, createDataStreamResponse } from 'ai'
import { azure } from '@ai-sdk/azure'
import { AIPaneChatRequest } from './types'
import { createAIUsage } from '@/app/server-actions/log_ai_usage'
import { AIUsageType } from '@prisma/client'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/app/api/auth/authOptions'
import {
  persistConversationTurn,
  createExecutionStepCollector,
  addMessageToConversation,
} from '@/app/server-actions/ai-chat'
import {
  buildSearchTools,
  type SearchProgressCallback,
} from '@/app/api/dragtree/shared/search-tools'
import { type SearchMetadata } from '@/app/api/dragtree/shared/brave-search'
import { isRateLimited, getRetryAfterSeconds } from '@/app/libs/rateLimiter'
import { standardErrors } from '../shared/errors'
import { buildContextMessages } from '@/app/libs/llmContext'

export const maxDuration = 60

/**
 * Extract and validate request data from the raw request
 * @param requestData - The parsed request data
 * @returns Validated data object
 * @throws Error if validation fails
 */
function extractAndValidateRequestData(requestData: AIPaneChatRequest) {
  // Extract request data
  const rawMessages = requestData.messages
  const model = requestData.model || 'gpt-4.1'
  const context = requestData.context
  const settings = requestData.settings || {}
  const conversationId = requestData.conversationId

  // Validate raw messages
  if (!rawMessages || !Array.isArray(rawMessages)) {
    throw new Error('INVALID_MESSAGES')
  }

  // Validate messages array is not empty
  if (rawMessages.length === 0) {
    throw new Error('EMPTY_MESSAGES')
  }

  // ConversationId is mandatory now to avoid duplicate rows
  if (!conversationId || !conversationId.startsWith('thread_')) {
    throw new Error('INVALID_CONVERSATION_ID')
  }

  return {
    rawMessages,
    model,
    context,
    settings,
    conversationId,
  }
}

export async function POST(req: Request) {
  try {
    // Retrieve the authenticated user session to obtain userId for logging
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return standardErrors.unauthorized()
    }
    const userId = session.user.id

    // Rate limiting - 5 requests per minute per user for chat
    const rateLimitKey = `${userId}:aipane:chat`
    if (isRateLimited(rateLimitKey, 60000)) {
      // 1 minute window
      const retryAfter = getRetryAfterSeconds(rateLimitKey, 60000)
      return standardErrors.rateLimited(retryAfter, '5')
    }

    // Parse request body with proper typing
    let requestData: AIPaneChatRequest
    try {
      const rawData = await req.json()
      requestData = rawData as AIPaneChatRequest
    } catch (error) {
      console.error('🤖 [Chat Real] Failed to parse JSON:', error)
      return standardErrors.invalidJson()
    }

    if (!requestData) {
      return standardErrors.missingBody()
    }

    console.log('🤖 [Chat Real] Received request:', {
      messageCount: requestData.messages?.length || 0,
      model: requestData.model,
      hasContext: !!requestData.context,
      conversationId: requestData.conversationId,
    })

    // Extract and validate request data
    let rawMessages: any[]
    let model: string
    let context: string | undefined
    let settings: Record<string, any>
    let conversationId: string

    try {
      const validatedData = extractAndValidateRequestData(requestData)
      rawMessages = validatedData.rawMessages
      model = validatedData.model
      context = validatedData.context
      settings = validatedData.settings
      conversationId = validatedData.conversationId
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'UNKNOWN_ERROR'
      switch (errorMessage) {
        case 'INVALID_MESSAGES':
          return standardErrors.invalidMessages()
        case 'EMPTY_MESSAGES':
          return standardErrors.invalidMessages(
            'Messages array must contain at least one message'
          )
        case 'INVALID_CONVERSATION_ID':
          return standardErrors.invalidIdFormat('conversationId', 'thread_*')
        default:
          return standardErrors.internalError('Validation error')
      }
    }

    console.log(
      '🤖 [Chat Real] Processing chat with model:',
      model,
      'Context:',
      context ? 'provided' : 'none',
      'ConversationId:',
      conversationId || 'none'
    )

    // (auto-creation removed – must be created via /conversations)

    // Prepare messages for AI model
    const latestUser = rawMessages[rawMessages.length - 1]

    // Helper function to normalize content for buildContextMessages (which expects string)
    const normalizeContentForContext = (content: any): string => {
      if (typeof content === 'string') return content
      if (Array.isArray(content)) {
        return content
          .map(part => (typeof part === 'string' ? part : part.text || ''))
          .join('')
      }
      return String(content)
    }

    // Build the base message list (system prompt + history + latest user msg)
    const { messages: baseMessages } = await buildContextMessages(
      conversationId,
      {
        role: 'user',
        content: normalizeContentForContext(latestUser.content),
      },
      100_000
    )

    // ──────────────────────────────────────────────────────────
    // Combine system prompt + context into ONE message to reduce tokens.
    // 1. Start with default system prompt (always baseMessages[0]).
    // 2. Determine context text – from current request or from the first
    //    persisted system message that starts with "CONTEXT:".
    // 3. Build a merged prompt:
    //    "You are …\n\nWe did some quick researches, below are the findings:\n<ctx>"
    // 4. Remove the standalone CONTEXT system message (if any).
    // ──────────────────────────────────────────────────────────

    const defaultPrompt = baseMessages[0]

    // Try to get context from request first (brand-new convo)
    let ctxText: string | undefined =
      context && context.trim().length > 0 ? context : undefined

    // Otherwise look for a persisted CONTEXT message
    if (!ctxText && baseMessages.length > 1) {
      const maybeCtx = baseMessages[1]
      if (
        maybeCtx.role === 'system' &&
        maybeCtx.content.startsWith('CONTEXT:\n')
      ) {
        ctxText = maybeCtx.content.replace(/^CONTEXT:\n/, '')
      }
    }

    let mergedSystemContent = defaultPrompt.content
    if (ctxText) {
      mergedSystemContent += `\n\nWe did some quick researches, below are the findings:\n${ctxText}`
    }

    // Build final message array: merged system + rest without duplicate ctx msg
    const modelMessages = [
      {
        role: 'system' as const,
        content: mergedSystemContent,
      },
      // Skip the old context system msg if we merged it (index 1)
      ...baseMessages.slice(ctxText ? 2 : 1),
    ]

    // Initialize execution step collector and search metadata
    const stepCollector = createExecutionStepCollector()
    const searchMetadata: SearchMetadata[] = []
    let finalResponse = ''

    // Note: Real-time step streaming will be implemented in future version

    // Create search progress callback for execution step tracking
    const searchProgressCallback: SearchProgressCallback = status => {
      console.log('🔍 [Chat] Search progress:', status)

      // Add search execution steps to collector for persistence
      if (status.type === 'searching') {
        stepCollector.addToolCall('web_search', { query: status.query })
      } else if (status.type === 'completed') {
        stepCollector.addToolResult('web_search', {
          query: status.query,
          resultCount: status.resultCount,
        })
      } else if (status.type === 'error') {
        stepCollector.addToolResult('web_search', { error: status.error })
      }
    }

    // Use createDataStreamResponse for real-time step streaming
    return createDataStreamResponse({
      execute: dataStream => {
        // Initialize streaming
        dataStream.writeData({
          type: 'stream-start',
          conversationId,
          timestamp: Date.now(),
        })

        // Use Azure OpenAI for real chat with search tools and atomic persistence
        const result = streamText({
          model: azure(model),
          messages: modelMessages,
          tools: buildSearchTools(searchMetadata, searchProgressCallback),
          maxSteps: 10, // fewer steps to avoid duplicate searches
          temperature: 0.7,
          maxTokens: 4000,
          // Stream tool calls in real-time
          onChunk: async chunk => {
            // Capture tool calls and add them to the execution step collector
            if (chunk.chunk.type === 'tool-call') {
              stepCollector.addToolCall(
                chunk.chunk.toolName,
                chunk.chunk.args,
                {
                  context: `Real-time tool call during streaming`,
                  priority: 'medium',
                }
              )

              // Stream the step data to the client
              dataStream.writeData({
                type: 'execution-step',
                step: {
                  type: 'TOOL_CALL',
                  toolName: chunk.chunk.toolName,
                  args: chunk.chunk.args,
                  toolCallId: chunk.chunk.toolCallId,
                  timestamp: Date.now(),
                },
              })
            }

            if (chunk.chunk.type === 'tool-result') {
              stepCollector.addToolResult(
                chunk.chunk.toolName,
                chunk.chunk.result,
                {
                  toolCallId: chunk.chunk.toolCallId,
                  success: !chunk.chunk.isError,
                  context: `Real-time tool result during streaming`,
                }
              )

              // Stream the step result to the client (optional - we're filtering these out in UI)
              dataStream.writeData({
                type: 'execution-step',
                step: {
                  type: 'TOOL_RESULT',
                  toolName: chunk.chunk.toolName,
                  result: chunk.chunk.result,
                  toolCallId: chunk.chunk.toolCallId,
                  timestamp: Date.now(),
                },
              })
            }
          },
          onFinish: async result => {
            // CRITICAL: This is where we do atomic persistence
            try {
              console.log(
                '🤖 [Chat Real] Stream finished, starting atomic persistence...'
              )

              // Notify client that streaming is complete
              dataStream.writeData({
                type: 'stream-finish',
                timestamp: Date.now(),
              })

              // Get the latest user message
              const userMessages = rawMessages.filter(m => m.role === 'user')
              const latestUserMessage =
                normalizeContentForContext(
                  userMessages[userMessages.length - 1]?.content
                ) || ''

              // Use the clean content directly from the result
              finalResponse = result.text

              // Add reasoning summary if there were any tool calls or steps
              if (stepCollector.getStepCount() > 0) {
                const summary = `Generated response using ${searchMetadata.length} web search(es) and ${stepCollector.getStepCount()} execution step(s)`
                stepCollector.addReasoningSummary(summary)

                // Note: Reasoning summary step added to collector for persistence
              }

              // Log execution summary
              const summary = stepCollector.getSummary()
              console.log('🤖 [Chat Real] Execution summary:', summary)

              // Persist the complete conversation turn atomically with basic retry logic
              const MAX_RETRIES = 3
              let attempt = 0
              let persistResult: Awaited<
                ReturnType<typeof persistConversationTurn>
              > | null = null

              const isFirstTurn = rawMessages.length === 1 // only the new user msg

              // Persist context once at the start of the conversation so that
              // future calls automatically receive it via buildContextMessages.
              if (isFirstTurn && context && context.trim().length > 0) {
                try {
                  await addMessageToConversation(conversationId!, {
                    role: 'SYSTEM',
                    content: `CONTEXT:\n${context}`,
                  })
                  console.log('📌 [Chat Real] Persisted context message.')
                } catch (ctxErr) {
                  console.warn(
                    '⚠️ [Chat Real] Failed to persist context message:',
                    ctxErr
                  )
                }
              }

              while (attempt < MAX_RETRIES) {
                attempt++
                // eslint-disable-next-line no-await-in-loop
                persistResult = await persistConversationTurn({
                  conversationId: conversationId!,
                  userMessage: {
                    role: 'USER',
                    content: latestUserMessage,
                  },
                  assistantMessage: {
                    role: 'ASSISTANT',
                    content: finalResponse,
                    steps: stepCollector.getSteps(),
                  },
                })

                if (persistResult.success) break

                console.warn(
                  `⚠️  [Chat Real] Persist attempt ${attempt} failed:`,
                  persistResult.error
                )
                // Back-off delay: 50 ms * attempt (very small, keeps overall latency minimal)
                // eslint-disable-next-line no-await-in-loop
                await new Promise(res => setTimeout(res, 50 * attempt))
              }

              if (!persistResult?.success) {
                // CRITICAL: Log this failure – user saw response but it wasn't saved
                console.error(
                  'FATAL: Failed to save conversation turn to database after retries.',
                  { conversationId, error: persistResult?.error }
                )
              } else {
                console.log(
                  '🤖 [Chat Real] Successfully persisted conversation turn:',
                  {
                    userMessageId: persistResult.data?.userMessageId,
                    assistantMessageId: persistResult.data?.assistantMessageId,
                    stepsCount: stepCollector.getStepCount(),
                  }
                )
              }

              // Log AI usage for monitoring (keep existing logic)
              try {
                await createAIUsage({
                  userId: userId,
                  entityType: 'aipane',
                  entityId: conversationId!,
                  aiProvider: 'azure_openai',
                  modelName: model,
                  usageType: AIUsageType.CHAT,
                  inputPrompt: latestUserMessage,
                  messages: [
                    ...modelMessages,
                    { role: 'assistant', content: finalResponse },
                  ],
                  metadata: {
                    endpoint: '/api/aipane/chat',
                    promptTokens: result.usage?.promptTokens || 0,
                    completionTokens: result.usage?.completionTokens || 0,
                    totalTokens: result.usage?.totalTokens || 0,
                    finishReason: result.finishReason,
                    hasContext: !!context,
                    messageCount: rawMessages.length,
                    conversationId,
                    executionSteps: stepCollector.getStepCount(),
                    hasThinking:
                      stepCollector.getSummary().stepsByType.THOUGHT > 0,
                    hasToolCalls:
                      stepCollector.getSummary().stepsByType.TOOL_CALL > 0,
                    searchResults: searchMetadata.length,
                  },
                  config: settings,
                })
              } catch (error) {
                console.error('Failed to log AI usage:', error)
              }
            } catch (error) {
              // CRITICAL: Log this failure - the user saw a response but it wasn't saved
              console.error('FATAL: Failed to process conversation turn.', {
                conversationId,
                error,
              })
            }
          },
        })

        // Merge the streamText result into the data stream
        result.mergeIntoDataStream(dataStream)
      },
      onError: error => {
        console.error('🤖 [Chat Real] Stream error:', error)
        return error instanceof Error ? error.message : String(error)
      },
    })
  } catch (error) {
    console.error('Chat API error:', error)
    return standardErrors.internalError('Error generating response')
  }
}
